// Main component
export { default as ProductsTable } from "./products-table-new";

// Individual components (for custom usage)
export { TableToolbar } from "./table-toolbar";
export { TableContent } from "./table-content";
export { TablePagination } from "./table-pagination";
export { TableFilters } from "./table-filters";
export { RowActions } from "./row-actions";
export { LoadingState, ErrorState } from "./table-states";

// Column definitions
export { columns } from "./columns";

// Types and utilities
export type { Product, RouterOutputs, ProductsResponse } from "./types";
export { multiColumnFilterFn } from "./utils";
