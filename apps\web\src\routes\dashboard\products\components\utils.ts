import type { FilterFn } from "@tanstack/react-table";
import type { Product } from "./types";

// Custom filter function for multi-column searching
export const multiColumnFilterFn: FilterFn<Product> = (row, columnId, filterValue) => {
  const searchableRowContent = `${row.original.name} ${
    row.original.description || ""
  } ${row.original.slug}`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};
