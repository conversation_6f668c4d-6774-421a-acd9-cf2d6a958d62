# Products Table - Modular Structure

## 🏗️ **Architecture Overview**

The products table has been refactored into a modular structure for better maintainability and reusability.

## 📁 **File Structure**

```
components/
├── index.ts                    # Main exports
├── products-table.tsx          # Legacy re-export (for compatibility)
├── products-table-new.tsx      # Main table component
├── types.ts                    # TypeScript types
├── utils.ts                    # Utility functions
├── columns.tsx                 # Column definitions
├── row-actions.tsx             # Row action menu
├── table-states.tsx            # Loading & error states
├── table-filters.tsx           # Search filter component
├── table-toolbar.tsx           # Toolbar with filters & actions
├── table-content.tsx           # Table content & headers
├── table-pagination.tsx        # Pagination controls
└── README.md                   # Original documentation
```

## 📦 **Component Breakdown**

### **Core Components**

#### `ProductsTable` (products-table-new.tsx)
- Main orchestrator component
- Manages state (pagination, sorting, filtering)
- Handles tRPC data fetching
- Composes all sub-components

#### `TableContent` (table-content.tsx)
- Renders the actual table structure
- Handles headers with sorting indicators
- Renders table rows and cells
- Shows "no data" state

#### `TableToolbar` (table-toolbar.tsx)
- Contains search filters
- Column visibility toggle
- Bulk action buttons (delete, add)
- Responsive layout

#### `TablePagination` (table-pagination.tsx)
- Page size selector
- Navigation buttons (first, prev, next, last)
- Results counter display

### **Feature Components**

#### `TableFilters` (table-filters.tsx)
- Search input with icon
- Clear filter button
- Real-time search functionality

#### `RowActions` (row-actions.tsx)
- Dropdown menu for each row
- Actions: view, edit, manage variants/stock, duplicate, delete
- Keyboard shortcuts

#### `LoadingState` & `ErrorState` (table-states.tsx)
- Skeleton loading animation
- Error message display
- Consistent UX patterns

### **Configuration**

#### `columns.tsx`
- Column definitions with types
- Cell renderers for each column
- Sorting and filtering configuration

#### `types.ts`
- tRPC inferred types
- Product type definition
- Router output types

#### `utils.ts`
- Multi-column filter function
- Reusable utility functions

## 🚀 **Usage Examples**

### **Basic Usage (Recommended)**
```typescript
import ProductsTable from "./components/products-table";

function ProductsPage() {
  return <ProductsTable />;
}
```

### **Using Individual Components**
```typescript
import { 
  TableContent, 
  TableToolbar, 
  TablePagination,
  columns,
  type Product
} from "./components";
import { useReactTable } from "@tanstack/react-table";

function CustomProductsTable() {
  // Your custom table setup
  const table = useReactTable<Product>({
    data: products,
    columns,
    // ... other config
  });

  return (
    <div className="space-y-4">
      <TableToolbar table={table} id="custom-table" />
      <TableContent table={table} />
      <TablePagination table={table} totalCount={totalCount} id="custom-table" />
    </div>
  );
}
```

### **Extending Columns**
```typescript
import { columns as baseColumns } from "./components";
import type { ColumnDef } from "@tanstack/react-table";
import type { Product } from "./components/types";

const customColumns: ColumnDef<Product>[] = [
  ...baseColumns,
  {
    header: "Custom Field",
    accessorKey: "customField",
    cell: ({ row }) => <span>{row.original.customField}</span>,
  },
];
```

### **Custom Row Actions**
```typescript
import { RowActions } from "./components";
import type { Row } from "@tanstack/react-table";
import type { Product } from "./components/types";

function CustomRowActions({ row }: { row: Row<Product> }) {
  return (
    <DropdownMenu>
      {/* Your custom actions */}
    </DropdownMenu>
  );
}
```

## 🎯 **Benefits**

### **Maintainability**
- **Single Responsibility**: Each component has one clear purpose
- **Easy Testing**: Components can be tested in isolation
- **Focused Changes**: Modifications are scoped to relevant files

### **Reusability**
- **Component Composition**: Mix and match as needed
- **Cross-Project Usage**: Components can be used in other tables
- **Consistent Patterns**: Standardized table components

### **Developer Experience**
- **Better IntelliSense**: Smaller files with focused imports
- **Easier Debugging**: Issues are isolated to specific components
- **Cleaner Git History**: Changes are scoped appropriately

### **Customization**
- **Flexible Architecture**: Easy to extend or modify
- **Theme Consistency**: Centralized styling patterns
- **Type Safety**: Full TypeScript support throughout

## 🔄 **Migration Guide**

The original `products-table.tsx` now re-exports the new modular version, so existing imports continue to work:

```typescript
// This still works unchanged
import ProductsTable from "./components/products-table";
```

For new development, you can import individual components:

```typescript
// New modular approach
import { ProductsTable, TableContent, columns } from "./components";
```

## 🧪 **Testing Strategy**

Each component can now be tested independently:

```typescript
// Test individual components
import { TableFilters } from "./components";
import { render, screen } from "@testing-library/react";

test("TableFilters renders search input", () => {
  render(<TableFilters table={mockTable} id="test" />);
  expect(screen.getByPlaceholderText(/filter by name/i)).toBeInTheDocument();
});
```

## 📈 **Future Enhancements**

The modular structure makes it easy to add new features:

- **Export Functionality**: Add export buttons to toolbar
- **Advanced Filters**: Create specialized filter components
- **Custom Views**: Different table layouts for different use cases
- **Bulk Operations**: More bulk action components
